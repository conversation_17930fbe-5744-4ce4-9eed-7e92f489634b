import { <PERSON><PERSON>, Card, Radio, Tag, Tooltip } from "antd";
import { <PERSON><PERSON><PERSON>, <PERSON>, Eye, Palette, RefreshCw } from "lucide-react";
import React, { useEffect, useState } from "react";
import EditorSnippet from "../../common/EditorSnippet";
import {
  cssPlaceholder,
  getPreviewHTML,
  htmlPlaceholder,
  jsPlaceholder,
  tabList,
} from "../content";
import PreviewTab from "./PreviewTab";

const TabList = ({ formData, setFormData }) => {
  const [placeholderInput, setPlaceholderInput] = useState("");
  const [previewMode, setPreviewMode] = useState("code");

  const tabContents = {
    code: {
      key: "html_content",
      type: "html",
      textareaId: "html-editor",
      placeholder: htmlPlaceholder,
      label: "HTML Code Editor",
      icon: <Code className="tw-w-4 tw-h-4 tw-text-orange-600 tw-mr-2" />,
    },
    css: {
      key: "css_content",
      type: "css",
      textareaId: "css-editor",
      placeholder: cssPlaceholder,
      label: "CSS",
      icon: <Palette className="tw-w-4 tw-h-4 tw-text-blue-600 tw-mr-2" />,
    },
    javascript: {
      key: "js_content",
      type: "js",
      textareaId: "js-editor",
      placeholder: jsPlaceholder,
      label: "JavaScript",
      icon: <Braces className="tw-w-4 tw-h-4 tw-text-yellow-600 tw-mr-2" />,
    },
    preview: {
      key: "preview",
      label: "Live Preview",
      icon: <Eye className="tw-w-4 tw-h-4 tw-text-green-600 tw-mr-2" />,
      content: <PreviewTab formData={formData} />,
    },
  };
  console.log(formData);
  const addPlaceholder = () => {
    if (
      placeholderInput.trim() &&
      !formData.placeholders.includes(placeholderInput.trim())
    ) {
      setFormData({
        ...formData,
        placeholders: [...formData.placeholders, placeholderInput.trim()],
      });
      setPlaceholderInput("");
    }
  };

  const removePlaceholder = (index) => {
    setFormData({
      ...formData,
      placeholders: formData?.placeholders.filter((_, i) => i !== index),
    });
  };

  const autoDetectPlaceholders = () => {
    const content = formData?.html_content;
    // if (!content) return;
    const regex = /\$\{([^}]+)\}/g;
    const matches = [];
    let match;

    while ((match = regex.exec(content)) !== null) {
      if (!matches.includes(match[1])) {
        matches.push(match[1]);
      }
    }
    // ...formData.placeholders,
    setFormData({
      ...formData,
      placeholders: [...new Set([...matches])],
    });
  };
  useEffect(() => {
    const timeout = setTimeout(() => {
      // if (formData?.html_content) {
      console.log("isCallSignatureDeclaration....");
      autoDetectPlaceholders();
      // }
    }, 1000);

    return () => clearTimeout(timeout);
  }, [formData?.html_content]);

  return (
    <>
      {/* Placeholders Management */}
      {/* <Card
                title="Placeholders"
                className="tw-shadow-sm"
                extra={
                  <Button
                    type="link"
                    onClick={autoDetectPlaceholders}
                    icon={<RefreshCw className="tw-w-4 tw-h-4" />}
                    size="small"
                  >
                    Auto Detect
                  </Button>
                }
                headStyle={{
                  fontSize: "18px",
                  fontWeight: "600",
                  color: "#111827",
                }}
              >
                <div className="tw-flex tw-space-x-2 tw-mb-3">
                  <Input
                    value={placeholderInput}
                    onChange={(e) => setPlaceholderInput(e.target.value)}
                    onPressEnter={(e) => {
                      e.preventDefault();
                      addPlaceholder();
                    }}
                    placeholder="e.g., title, content, image_url"
                    size="large"
                    className="tw-flex-1"
                  />
                  <Button type="primary" onClick={addPlaceholder} size="large">
                    Add
                  </Button>
                </div>

                <div className="tw-flex tw-flex-wrap tw-gap-2">
                  {formData.placeholders.map((placeholder, index) => (
                    <span
                      key={index}
                      className="tw-inline-flex tw-items-center tw-px-3 tw-py-1 tw-bg-blue-100 tw-text-blue-800 tw-text-sm tw-rounded-full"
                    >
                      ${placeholder}
                      <button
                        type="button"
                        onClick={() => removePlaceholder(index)}
                        className="tw-ml-2 tw-text-blue-600 tw-hover:tw-text-blue-800"
                      >
                        <X className="tw-w-3 tw-h-3" />
                      </button>
                    </span>
                  ))}
                </div>

                {formData.placeholders.length === 0 && (
                  <p className="tw-text-sm tw-text-gray-500 tw-italic">
                    Add placeholders like ${"{title}"} or ${"{content}"} to make
                    your component dynamic
                  </p>
                )}
              </Card> */}
      {/* Right Panel - Code and Preview */}
      <div className="tw-space-y-4">
        {/* Preview/Code Toggle */}
        <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-p-4">
          <Radio.Group
            value={previewMode}
            onChange={(e) => setPreviewMode(e.target.value)}
            buttonStyle="solid"
            size="large"
            className="component-tab-list tw-w-full tw-p-[2px] tw-border tw-border-[#2563EB] tw-rounded-[10px]"
          >
            {tabList.map((tab) => (
              <Radio.Button
                key={tab.key}
                value={tab.key}
                className="tw-flex-1 tw-text-center !tw-rounded-[10px] before:!tw-w-0 tw-border-0 border-b-"
                style={{ width: "25%" }}
              >
                <div className="tw-flex tw-items-center tw-justify-center">
                  {tab.icon}
                  {tab?.tab}
                </div>
              </Radio.Button>
            ))}
          </Radio.Group>
        </div>
        <div className="tw-space-y-4">
          <Card className="tw-shadow-sm">
            <div className="tw-flex tw-items-center tw-mb-4">
              {tabContents[previewMode].icon}
              <span className="tw-text-sm tw-font-medium tw-text-gray-900">
                {tabContents[previewMode].label}
              </span>
            </div>
            {tabContents[previewMode].content ? (
              tabContents[previewMode].content
            ) : (
              <div className="tw-mb-4 tw-border tw-border-gray-300 tw-rounded-lg tw-overflow-hidden">
                <EditorSnippet
                  type={tabContents[previewMode].type}
                  defaultValue={formData?.[tabContents[previewMode].key]}
                  onValueChange={(code) => {
                    setFormData({
                      ...formData,
                      [tabContents[previewMode].key]: code,
                    });
                  }}
                  value={formData?.[tabContents[previewMode].key]}
                  placeholder={tabContents[previewMode].placeholder}
                  textareaId={tabContents[previewMode].textareaId}
                />
              </div>
            )}
            <div className="tw-w-full">
              <div className="tw-w-full tw-flex tw-items-center ">
                <p className="tw-mb-[2px]">Placeholders:</p>
                {/* <Button
                  type="link"
                  onClick={autoDetectPlaceholders}
                  icon={<RefreshCw className="tw-w-4 tw-h-4" />}
                  size="small"
                >
                  Auto Detect
                </Button> */}
              </div>

              {formData?.placeholders?.map((placeholder, index) => (
                <Tag color="blue" key={index}>
                  ${placeholder}
                </Tag>
              ))}
            </div>
          </Card>
        </div>
      </div>
    </>
  );
};

export default TabList;
