import React, { useState, useEffect } from "react";
import DragDropBuilder from "./DragDropBuilder";
import {
  Card,
  Row,
  Col,
  Button,
  Input,
  Typography,
  Space,
  Divider,
  Tag,
  Tooltip,
  Popconfirm,
  Empty,
  Spin,
  Badge,
  message,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  FileTextOutlined,
  CalendarOutlined,
  TagOutlined,
} from "@ant-design/icons";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import { Edit2 } from "lucide-react";

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;

const PageBuilder = () => {
  const [pages, setPages] = useState([]);
  const [showBuilder, setShowBuilder] = useState(false);
  const [editingPage, setEditingPage] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const api = useHttp();

  useEffect(() => {
    // Fetch pages
    api.sendRequest(CONSTANTS.API.pages.get, (res) => {
      console.log("Pages fetched:", res);
      setPages(res);
    });
  }, []);

  const refreshPages = () => {
    api.sendRequest(CONSTANTS.API.pages.get, (res) => {
      setPages(res);
    });
  };

  const handleEdit = (page) => {
    setEditingPage(page);
    setShowBuilder(true);
  };

  const handleDelete = async (id) => {
    api.sendRequest(
      apiGenerator(CONSTANTS.API.pages.delete, { id }),
      (res) => {
        console.log("Page deleted successfully:", res);
        message.success("Page deleted successfully!");
        refreshPages();
      },
      null,
      null,
      (error) => {
        console.error("Error deleting page:", error);
        message.error("Failed to delete page. Please try again.");
      }
    );
  };

  const handleSave = async () => {
    refreshPages();
    setShowBuilder(false);
    setEditingPage(null);
  };

  const filteredPages = pages.filter(
    (page) =>
      page.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      page.slug.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (api.isLoading) {
    return (
      <div className="tw-flex tw-justify-center tw-items-center tw-h-96">
        <Spin size="large" tip="Loading pages..." />
      </div>
    );
  }

  if (showBuilder) {
    return (
      <div className="">
        <DragDropBuilder
          page={editingPage}
          onSave={handleSave}
          onCancel={() => {
            setShowBuilder(false);
            setEditingPage(null);
          }}
        />
      </div>
    );
  }

  return (
    <div className="tw-p-6 tw-bg-gray-50 tw-min-h-screen">
      <div className="tw-max-w-7xl tw-mx-auto">
        {/* Header Section */}
        <div className="tw-mb-8">
          <div className="tw-flex  tw-justify-between tw-items-start tw-lg:tw-items-center tw-gap-4">
            <div>
              <Title level={2} className="!tw-mb-2">
                Pages ({filteredPages.length})
              </Title>
            </div>

            <Button
              type="primary"
              size="large"
              icon={<PlusOutlined />}
              onClick={() => setShowBuilder(true)}
              className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
              // className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-border-0 tw-h-12 tw-px-6 tw-rounded-lg tw-font-medium hover:tw-from-blue-700 hover:tw-to-purple-700"
            >
              Create Page
            </Button>
          </div>
          {/* <Divider className="tw-my-6" /> */}
        </div>

        {/* Search and Filter Section */}
        <div className="tw-mb-6">
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={24} md={24}>
              <Search
                placeholder="Search pages by name or slug..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="large"
                // className="search-input-enhanced"
                allowClear
              />
            </Col>
          </Row>
        </div>

        {/* Pages Grid */}
        <Row gutter={[24, 24]}>
          {filteredPages.map((page) => (
            <Col xs={24} sm={12} lg={8} xl={8} xxl={8} key={"f"}>
              <Card
                className="tw-h-full tw-shadow-sm hover:tw-shadow-lg tw-transition-all tw-duration-300 tw-border-0 tw-rounded-2xl"
                styles={{
                  body: {
                    padding: "24px",
                  },
                }}
              >
                <div className="tw-mb-4 tw-w-full tw-flex tw-items-center tw-justify-between">
                  <span className="tw-border-[1px] tw-rounded-full tw-border-solid tw-border-[#D9DBDF] tw-px-2 tw-py-1 tw-text-xs tw-text-black">
                    v{page.version}
                  </span>
                  <div className="">
                    <Tooltip title="Edit Page" key="edit">
                      <Button
                        type="text"
                        icon={<Edit2 color="#2563eb" width={16} height={16} />}
                        onClick={() => handleEdit(page)}
                        className="tw-text-blue-600 hover:tw-text-blue-700"
                      />
                    </Tooltip>
                    <Tooltip title="Delete Page" key="delete">
                      <Popconfirm
                        title="Delete Page"
                        description="Are you sure you want to delete this page?"
                        onConfirm={() => handleDelete(page?.id)}
                        okText="Yes"
                        cancelText="No"
                        okButtonProps={{ danger: true }}
                      >
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          className="tw-text-red-600 hover:tw-text-red-700"
                        />
                      </Popconfirm>
                    </Tooltip>
                  </div>
                  {/* <Tag color="blue" className="tw-mb-2">
                    /{page.slug}
                  </Tag> */}
                </div>

                <div className="tw-flex tw-justify-between tw-items-center tw-text-xs tw-text-gray-500">
                  <Space>
                    <Title level={4} className="!tw-mb-2 tw-truncate">
                      {page.name}
                    </Title>
                  </Space>
                  <Space>
                    <span className="tw-text-base">
                      {new Date(page.created_at).toLocaleDateString()}
                    </span>
                  </Space>
                </div>
              </Card>
            </Col>
          ))}
        </Row>

        {/* Empty State */}
        {filteredPages.length === 0 && (
          <div className="tw-text-center tw-py-16">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div>
                  <Title level={4} className="!tw-mb-2">
                    {searchTerm ? "No pages found" : "No pages yet"}
                  </Title>
                  <Text type="secondary" className="tw-text-base">
                    {searchTerm
                      ? "Try adjusting your search criteria"
                      : "Create your first page using the drag-and-drop builder"}
                  </Text>
                </div>
              }
            ></Empty>
          </div>
        )}
      </div>
    </div>
  );
};

export default PageBuilder;
