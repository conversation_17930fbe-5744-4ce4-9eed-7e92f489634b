import { X } from "lucide-react";
import React from "react";

const PageSetting = ({
  showSettings,
  setShowSettings,
  pageData,
  setPageData,
}) => {
  return (
    <div className="tw-fixed tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-flex tw-items-center tw-justify-center tw-z-50">
      <div className="tw-bg-white tw-rounded-xl tw-p-6 tw-w-full tw-max-w-2xl tw-m-4 tw-max-h-screen tw-overflow-y-auto">
        <div className="tw-flex tw-justify-between tw-items-center tw-mb-6">
          <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900">
            Page Settings
          </h3>
          <button
            onClick={() => setShowSettings(false)}
            className="tw-text-gray-400 tw-hover:tw-text-gray-600"
          >
            <X className="tw-w-5 tw-h-5" />
          </button>
        </div>

        <div className="tw-space-y-4">
          <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-gap-4">
            <div>
              <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                Page Name *
              </label>
              <input
                type="text"
                value={pageData.name}
                onChange={(e) =>
                  setPageData({ ...pageData, name: e.target.value })
                }
                className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                placeholder="e.g., Home, About, Contact"
                required
              />
            </div>

            <div>
              <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                URL Slug *
              </label>
              <input
                type="text"
                value={pageData.slug}
                onChange={(e) =>
                  setPageData({ ...pageData, slug: e.target.value })
                }
                className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                placeholder="e.g., home, about, contact"
                required
              />
            </div>
          </div>

          <div>
            <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
              Meta Title
            </label>
            <input
              type="text"
              value={pageData.meta_title}
              onChange={(e) =>
                setPageData({ ...pageData, meta_title: e.target.value })
              }
              className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
              placeholder="SEO title for this page"
            />
          </div>

          <div>
            <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
              Meta Description
            </label>
            <textarea
              value={pageData.meta_description}
              onChange={(e) =>
                setPageData({
                  ...pageData,
                  meta_description: e.target.value,
                })
              }
              className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-h-20"
              placeholder="Brief description for search engines"
              rows="3"
            />
          </div>

          <div>
            <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
              Custom CSS
            </label>
            <textarea
              value={pageData.custom_css}
              onChange={(e) =>
                setPageData({ ...pageData, custom_css: e.target.value })
              }
              className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-h-32 tw-font-mono tw-text-sm"
              placeholder="/* Custom CSS for this page */"
              rows="6"
            />
          </div>

          <div>
            <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
              Custom JavaScript
            </label>
            <textarea
              value={pageData.custom_js}
              onChange={(e) =>
                setPageData({ ...pageData, custom_js: e.target.value })
              }
              className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-h-32 tw-font-mono tw-text-sm"
              placeholder="// Custom JavaScript for this page"
              rows="6"
            />
          </div>
        </div>

        <div className="tw-flex tw-justify-end tw-space-x-3 tw-mt-6">
          <button
            onClick={() => setShowSettings(false)}
            className="tw-px-4 tw-py-2 tw-border tw-border-gray-300 tw-text-gray-700 tw-rounded-lg tw-font-medium tw-hover:tw-bg-gray-50 tw-transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default PageSetting;
