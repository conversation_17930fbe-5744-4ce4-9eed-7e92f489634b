import { Input, Tooltip } from "antd";
import { ChevronLeft, GripVertical, Trash2 } from "lucide-react";
import React, { useRef, useState } from "react";
import { useDrag, useDrop } from "react-dnd";

const PageStructure = ({
  isStructureOpen,
  setIsStructureOpen,
  pageData,
  setPageData,
  components,
  handleCssChange,
  removeComponentFromPage,
}) => {
  const [isDraggingStructure, setIsDraggingStructure] = useState(false);
  const [tempComponents, setTempComponents] = useState([]);

  // Debounced move for page structure - using useRef to store latest state
  const dragStateRef = useRef({
    isDragging: false,
    draggedIndex: -1,
    components: [],
  });

  const moveComponentInStructure = (fromIndex, toIndex) => {
    console.log(`Structure move: ${fromIndex} -> ${toIndex}`);

    // Get current components (either from temp state or pageData)
    const currentComponents = dragStateRef.current.isDragging
      ? dragStateRef.current.components
      : pageData.components;

    const updatedComponents = [...currentComponents];
    const [movedComponent] = updatedComponents.splice(fromIndex, 1);
    updatedComponents.splice(toIndex, 0, movedComponent);

    // Store in ref for immediate visual feedback
    dragStateRef.current.components = updatedComponents;

    // Update temp state for UI
    setTempComponents(updatedComponents);
  };

  // Start structure drag
  const startStructureDrag = (index) => {
    console.log("Starting structure drag for index:", index);
    dragStateRef.current = {
      isDragging: true,
      draggedIndex: index,
      components: [...pageData.components],
    };
    setIsDraggingStructure(true);
    setTempComponents([...pageData.components]);
  };

  // Finish structure drag and commit changes
  const finishStructureDrag = () => {
    console.log("Finishing structure drag");
    console.log("Current drag state:", dragStateRef.current);

    // Immediately mark as not dragging to prevent hover after drop
    const wasIsDragging = dragStateRef.current.isDragging;
    const componentsToCommit = [...dragStateRef.current.components];

    // Reset drag state FIRST to prevent race conditions
    dragStateRef.current = {
      isDragging: false,
      draggedIndex: -1,
      components: [],
    };
    setIsDraggingStructure(false);
    setTempComponents([]);

    // Then commit changes if there were any
    if (wasIsDragging && componentsToCommit.length > 0) {
      console.log("Committing changes to pageData:", componentsToCommit);
      setPageData((prevData) => {
        const newData = {
          ...prevData,
          components: componentsToCommit,
        };
        console.log("New pageData:", newData);
        return newData;
      });
    } else {
      console.log("No changes to commit");
    }

    console.log("Drag state reset");
  };

  // Reorderable structure item - FIXED with proper dependencies and debugging
  const StructureItem = ({ key, index, componentName, onRemove }) => {
    const ref = useRef(null);

    // Drop zone for receiving dragged items
    const [{ isOver }, drop] = useDrop(
      () => ({
        accept: "STRUCT_ITEM",
        hover(item, monitor) {
          // Prevent hover after drop by checking drag state
          if (!dragStateRef.current.isDragging) {
            console.log("Ignoring hover - not in drag state");
            return;
          }

          if (!ref.current) {
            console.log("No ref available for hover");
            return;
          }

          if (!monitor.isOver({ shallow: true })) {
            return;
          }

          const dragIndex = item.index;
          const hoverIndex = index;

          console.log(`Hover: drag=${dragIndex}, hover=${hoverIndex}`);

          // Don't replace items with themselves
          if (dragIndex === hoverIndex) return;

          // Determine rectangle on screen
          const hoverBoundingRect = ref.current.getBoundingClientRect();

          // Get vertical middle
          const hoverMiddleY =
            (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

          // Determine mouse position
          const clientOffset = monitor.getClientOffset();
          if (!clientOffset) return;

          // Get pixels to the top
          const hoverClientY = clientOffset.y - hoverBoundingRect.top;

          // Only perform the move when the mouse has crossed half of the items height
          // Dragging downwards
          if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
            return;
          }

          // Dragging upwards
          if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
            return;
          }

          console.log(`Performing move: ${dragIndex} -> ${hoverIndex}`);

          // Time to actually perform the action
          moveComponentInStructure(dragIndex, hoverIndex);

          // Update the item index
          item.index = hoverIndex;
        },
        collect: (monitor) => ({
          isOver: monitor.isOver(),
        }),
      }),
      [index, moveComponentInStructure]
    );

    // Drag source
    const [{ isDragging }, drag] = useDrag(
      () => ({
        type: "STRUCT_ITEM",
        item: () => {
          console.log(`Starting drag for item at index: ${index}`);
          // Initialize drag state
          startStructureDrag(index);
          return { index };
        },
        collect: (monitor) => ({
          isDragging: monitor.isDragging(),
        }),
        end: () => {
          console.log(`Ending drag for item at index: ${index}`);
          // Always finish drag regardless of drop result
          setTimeout(() => {
            finishStructureDrag();
          }, 0); // Use setTimeout to ensure this runs after any pending hover events
        },
      }),
      [index]
    );

    // Connect drag and drop refs
    drag(drop(ref));

    return (
      <div
        ref={ref}
        key={key}
        className={`tw-flex tw-items-center tw-justify-between tw-p-3 tw-rounded-lg tw-border tw-cursor-move tw-transition-all tw-duration-150 ${
          isDragging
            ? "tw-bg-blue-50 tw-border-blue-300 tw-shadow-lg"
            : isOver
            ? "tw-bg-yellow-50 tw-border-yellow-300"
            : "tw-bg-gray-50 tw-border-gray-200 tw-hover:tw-bg-gray-100"
        }`}
        style={{
          opacity: isDragging ? 0.8 : 1,
          transform: isDragging ? "rotate(2deg)" : "none",
        }}
      >
        <div className="tw-flex tw-items-center tw-flex-1">
          <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400 tw-mr-3 tw-flex-shrink-0" />
          <div className="tw-flex-1">
            <p className="tw-text-sm tw-font-medium tw-text-gray-900">
              {componentName || "Unknown Component"}
            </p>
            <p className="tw-text-xs tw-text-gray-500">Position {index + 1}</p>
            <div className="tw-mt-2">
              <Input
                size="small"
                placeholder="Enter CSS class"
                value={pageData.components[index]?.cssClass || ""}
                onChange={(e) => handleCssChange(index, e.target.value)}
                onMouseDown={(e) => e.stopPropagation()} // Prevent drag when clicking input
                onFocus={(e) => e.stopPropagation()}
              />
            </div>
          </div>
        </div>
        <button
          onClick={(e) => {
            e.stopPropagation(); // Prevent drag when clicking remove
            onRemove(index);
          }}
          className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-red-600 tw-rounded tw-transition-colors tw-flex-shrink-0 tw-ml-2"
        >
          <Trash2 className="tw-w-4 tw-h-4" />
        </button>
      </div>
    );
  };

  return (
    <>
      {/* Right Sidebar - Page Structure (collapsible, animated) */}
      {/* In-layout column with width animation */}
      <div
        className={`tw-bg-white tw-border-l tw-border-gray-200 tw-flex tw-flex-col tw-transition-all tw-duration-300 tw-ease-in-out ${
          isStructureOpen ? "tw-w-80" : "tw-w-0 tw-overflow-hidden"
        }`}
      >
        {isStructureOpen && (
          <div className=" tw-border-b tw-border-gray-200 tw-flex tw-items-center tw-justify-between">
            <div className="tw-p-4 tw-flex tw-items-start tw-flex-col tw-justify-start tw-text-start">
              <h3 className=" tw-text-lg tw-font-semibold tw-text-gray-900  ">
                Page Structure
              </h3>
              <p className="tw-text-sm tw-text-gray-600 tw-mt-1">
                {
                  (isDraggingStructure && tempComponents.length > 0
                    ? tempComponents
                    : pageData?.components
                  ).length
                }{" "}
                components
              </p>
            </div>
            <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-border-l tw-border-gray-200">
              <Tooltip
                title={
                  isStructureOpen
                    ? "Hide Page Structure"
                    : "Show Page Structure"
                }
              >
                <button
                  onClick={() => setIsStructureOpen((v) => !v)}
                  className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                >
                  <ChevronLeft
                    size={30}
                    className={` ${isStructureOpen ? "" : "tw-rotate-180 "}`}
                  />
                </button>
              </Tooltip>
            </div>
          </div>
        )}

        <div className="tw-flex-1 tw-overflow-y-auto tw-p-4">
          {(() => {
            // Use temp components during drag, actual components otherwise
            const currentComponents =
              isDraggingStructure && tempComponents.length > 0
                ? tempComponents
                : pageData?.components;

            return currentComponents.length > 0 ? (
              <div className="tw-space-y-2">
                {currentComponents.map((pageComp, index) => {
                  const component = components?.find(
                    (c) => c.id === pageComp.id
                  );
                  return (
                    <StructureItem
                      key={
                        pageComp.uniqueId || `fallback-${pageComp.id}-${index}`
                      }
                      index={index}
                      componentName={component?.name}
                      onRemove={removeComponentFromPage}
                    />
                  );
                })}
              </div>
            ) : (
              <div className="tw-text-center tw-py-8">
                <p className="tw-text-gray-500">No components added</p>
                <p className="tw-text-sm tw-text-gray-400 tw-mt-1">
                  Components will appear here as you add them
                </p>
              </div>
            );
          })()}
        </div>
      </div>
    </>
  );
};

export default PageStructure;
