import React, { useState, useEffect, useRef } from "react";
import { Radio, Tooltip, Input } from "antd";
import Header from "../Layout/Header";
import {
  Save,
  X,
  Eye,
  Smartphone,
  Tablet,
  Monitor,
  Settings,
  Trash2,
  GripVertical,
  ChevronLeft,
} from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import PageStructure from "./Component/PageStructure";
import PageSetting from "./Component/PageSetting";

const DragDropBuilder = ({ page, onSave, onCancel }) => {
  const api = useHttp();
  const [components, setComponents] = useState([]);
  const [categories, setCategories] = useState([]);
  const [pageData, setPageData] = useState({
    name: "",
    slug: "",
    meta_title: "",
    meta_description: "",
    custom_css: "",
    custom_js: "",
    components: [],
  });
  const [previewMode, setPreviewMode] = useState("desktop");
  const [showSettings, setShowSettings] = useState(false);
  const [saving, setSaving] = useState(false);
  const [isStructureOpen, setIsStructureOpen] = useState(true);

  useEffect(() => {
    // Fetch components
    api.sendRequest(CONSTANTS.API.components.get, (res) => {
      console.log("Components fetched:", res);
      setComponents(res);
    });

    // Fetch categories
    api.sendRequest(CONSTANTS.API.categories.get, (res) => {
      console.log("Categories fetched:", res);
      setCategories(res);
    });

    if (page) {
      // Ensure existing components have uniqueId for stable React keys
      const componentsWithUniqueId = (page.components || []).map(
        (comp, index) => ({
          ...comp,
          uniqueId:
            comp.uniqueId ||
            `${comp.id}-${index}-${Date.now()}-${Math.random()
              .toString(36)
              .substring(2, 11)}`,
        })
      );

      setPageData({
        name: page.name || "",
        slug: page.slug || "",
        meta_title: page.meta_title || "",
        meta_description: page.meta_description || "",
        custom_css: page.custom_css || "",
        custom_js: page.custom_js || "",
        components: componentsWithUniqueId,
      });
    }
  }, [page]);

  const handleSave = async () => {
    setSaving(true);

    const apiConfig = page
      ? apiGenerator(CONSTANTS.API.pages.update, { id: page.id })
      : CONSTANTS.API.pages.create;

    api.sendRequest(
      apiConfig,
      (res) => {
        console.log("Page saved successfully:", res);
        setSaving(false);
        onSave();
      },
      pageData,
      page ? "Page updated successfully!" : "Page created successfully!",
      (error) => {
        console.error("Error saving page:", error);
        setSaving(false);
      }
    );
  };

  const addComponentToPage = (component) => {
    console.log("Adding component to page:", component);

    const newPageComponent = {
      id: component.id,
      name: component.name,
      order: pageData.components.length,
      cssClass: "",
      uniqueId: `${component.id}-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 11)}`, // Unique ID for React keys
    };

    const updatedComponents = [...pageData.components, newPageComponent];
    console.log("Updated components:", updatedComponents);

    setPageData((prevData) => ({
      ...prevData,
      components: updatedComponents,
    }));
  };

  // Reorder helper for immediate component moves
  const moveComponent = (fromIndex, toIndex) => {
    console.log(`Moving component from ${fromIndex} to ${toIndex}`);
    const updatedComponents = [...pageData.components];
    const [movedComponent] = updatedComponents.splice(fromIndex, 1);
    updatedComponents.splice(toIndex, 0, movedComponent);

    setPageData((prevData) => ({
      ...prevData,
      components: updatedComponents,
    }));
  };

  const removeComponentFromPage = (index) => {
    console.log("Removing component at index:", index);
    const updatedComponents = pageData.components.filter((_, i) => i !== index);

    setPageData((prevData) => ({
      ...prevData,
      components: updatedComponents,
    }));
  };

  // Update CSS class for a specific component in page structure
  const handleCssChange = (index, value) => {
    const updated = [...pageData.components];
    updated[index] = { ...updated[index], cssClass: value };

    setPageData((prevData) => ({
      ...prevData,
      components: updated,
    }));
  };

  const getPreviewModeStyles = () => {
    switch (previewMode) {
      case "mobile":
        return "tw-w-80 tw-h-96";
      case "tablet":
        return "tw-w-96 tw-h-96";
      default:
        return "tw-w-full tw-h-96";
    }
  };

  const generatePreviewHTML = () => {
    let html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${pageData.meta_title || pageData.name}</title>
        <meta name="description" content="${pageData.meta_description}">
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
          ${pageData.custom_css}
        </style>
      </head>
      <body>
    `;

    pageData.components.forEach((pageComp) => {
      const component = components.find((c) => c.id === pageComp.id);
      if (component) {
        let componentHTML = component.html_content;

        // Replace placeholders with sample data
        if (component.placeholders) {
          component.placeholders.forEach((placeholder) => {
            const sampleData = getSampleData(placeholder);
            componentHTML = componentHTML.replace(
              new RegExp(`\\$\\{${placeholder}\\}`, "g"),
              sampleData
            );
          });
        }

        // Wrap with optional user CSS class
        const cssClass = pageComp.cssClass
          ? ` class="${pageComp.cssClass}"`
          : "";
        html += `<div${cssClass}>${componentHTML}</div>\n`;
      }
    });

    // Add empty state if no components
    if (pageData.components.length === 0) {
      html += `
        <div style="display: flex; align-items: center; justify-content: center; min-height: 100vh; text-align: center; color: #6b7280;">
          <div>
            <div style="font-size: 3rem; margin-bottom: 1rem;">📄</div>
            <p style="font-size: 1.125rem; margin-bottom: 0.5rem;">Drop components here to build your page</p>
            <p style="font-size: 0.875rem;">Drag components from the left panel</p>
          </div>
        </div>
      `;
    }

    html += `
        <script>
          ${pageData.custom_js}
        </script>
      </body>
      </html>
    `;

    return html;
  };

  const getSampleData = (placeholder) => {
    const lowerPlaceholder = placeholder.toLowerCase();

    if (
      lowerPlaceholder.includes("title") ||
      lowerPlaceholder.includes("heading")
    ) {
      return "Sample Page Title";
    } else if (lowerPlaceholder.includes("subtitle")) {
      return "This is a sample subtitle";
    } else if (
      lowerPlaceholder.includes("content") ||
      lowerPlaceholder.includes("text")
    ) {
      return "This is sample content that demonstrates how your page will look with real data.";
    } else if (
      lowerPlaceholder.includes("image") ||
      lowerPlaceholder.includes("img")
    ) {
      return "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=400&fit=crop";
    } else if (
      lowerPlaceholder.includes("button") ||
      lowerPlaceholder.includes("cta")
    ) {
      return "Learn More";
    } else {
      return `[${placeholder}]`;
    }
  };

  const groupedComponents = categories.reduce((acc, category) => {
    acc[category.id] = {
      name: category.name,
      color: category.color,
      components: components.filter((comp) => comp.category_id === category.id),
    };
    return acc;
  }, {});

  // React DnD types
  const DND_TYPES = {
    LIB_ITEM: "LIB_ITEM",
    STRUCT_ITEM: "STRUCT_ITEM",
  };

  // Draggable Component Library item - NO debouncing, immediate drag
  const LibraryItem = ({ comp }) => {
    const [{ isDragging }, drag] = useDrag(
      () => ({
        type: DND_TYPES.LIB_ITEM,
        item: () => {
          console.log("Starting library component drag:", comp.name);
          return { component: comp };
        },
        collect: (monitor) => ({ isDragging: monitor.isDragging() }),
        end: (_, monitor) => {
          if (monitor.didDrop()) {
            console.log("Library component dropped successfully:", comp.name);
          } else {
            console.log("Library component drag cancelled:", comp.name);
          }
        },
      }),
      [comp]
    );

    return (
      <div
        ref={drag}
        className="tw-p-3 tw-bg-gray-50 tw-rounded-lg tw-border tw-border-gray-200 tw-cursor-move tw-hover:tw-bg-gray-100 tw-hover:tw-border-gray-300 tw-transition-colors tw-select-none"
        style={{ opacity: isDragging ? 0.6 : 1 }}
      >
        <div className="tw-flex tw-items-center tw-justify-between">
          <div className="tw-flex-1">
            <p className="tw-text-sm tw-font-medium tw-text-gray-900">
              {comp.name}
            </p>
            <p className="tw-text-xs tw-text-gray-500">
              {comp.placeholders ? comp.placeholders.length : 0} placeholders
            </p>
          </div>
          <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" />
        </div>
      </div>
    );
  };

  // Preview Drop Zone Component - Improved for library components (NO debouncing)
  const PreviewDropZone = () => {
    const [{ isOver, canDrop }, drop] = useDrop(
      () => ({
        accept: DND_TYPES.LIB_ITEM,
        drop: (item, monitor) => {
          // Immediate drop without debouncing for library components
          if (monitor.didDrop()) return; // Prevent duplicate drops
          console.log("Dropping component:", item.component);
          addComponentToPage(item.component);
        },
        collect: (monitor) => ({
          isOver: monitor.isOver({ shallow: true }),
          canDrop: monitor.canDrop(),
        }),
      }),
      [pageData.components] // Dependency on components to ensure fresh state
    );

    const showDropIndicator = isOver && canDrop;

    return (
      <div
        ref={drop}
        className={`tw-absolute tw-inset-0 tw-z-10 tw-transition-all tw-duration-150 ${
          showDropIndicator
            ? "tw-bg-blue-50/60 tw-border-2 tw-border-dashed tw-border-blue-400"
            : ""
        }`}
        style={{
          minHeight: "400px",
          pointerEvents: "auto", // Always allow pointer events for drop zone
        }}
      >
        {showDropIndicator && (
          <div className="tw-flex tw-items-center tw-justify-center tw-h-full tw-pointer-events-none">
            <div className="tw-bg-white tw-rounded-lg tw-p-6 tw-shadow-xl tw-border-2 tw-border-blue-300 tw-animate-pulse">
              <div className="tw-flex tw-items-center tw-space-x-3">
                <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce"></div>
                <p className="tw-text-blue-700 tw-font-semibold tw-text-lg">
                  Drop to add component
                </p>
                <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce tw-animation-delay-100"></div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="tw-h-screen tw-flex tw-overflow-hidden">
        {/* Left Sidebar - Components Library */}
        <div className="tw-w-80 tw-bg-white tw-border-r tw-border-gray-200 tw-flex tw-flex-col">
          <div className="tw-p-4 tw-border-b tw-border-gray-200">
            <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-2">
              Component Library
            </h3>
            <p className="tw-text-xs tw-text-gray-600">
              Drag components to the canvas to build your page
            </p>
          </div>

          <div className="tw-flex-1 tw-overflow-y-auto tw-p-4">
            {Object.entries(groupedComponents).map(
              ([categoryId, categoryData]) => (
                <div key={categoryId} className="tw-mb-6">
                  <div className="tw-flex tw-items-center tw-mb-3">
                    <div
                      className="tw-w-3 tw-h-3 tw-rounded-full tw-mr-2"
                      style={{ backgroundColor: categoryData.color }}
                    />
                    <h4 className="tw-font-medium tw-text-gray-900">
                      {categoryData.name}
                    </h4>
                  </div>

                  <div className="tw-space-y-2">
                    {categoryData.components.map((component) => (
                      <LibraryItem key={component.id} comp={component} />
                    ))}
                  </div>
                </div>
              )
            )}

            {Object.keys(groupedComponents).length === 0 && (
              <div className="tw-text-center tw-py-8">
                <p className="tw-text-gray-500">No components available</p>
                <p className="tw-text-sm tw-text-gray-400 tw-mt-1">
                  Create components first to use them here
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Main Canvas Area */}
        <div className="tw-flex-1 tw-flex tw-flex-col">
          {/* Top Toolbar */}
          <div className="tw-flex tw-w-full tw-bg-white tw-border-b tw-border-gray-200 tw-space-x-4">
            <div className="tw-p-[21px]  tw-flex tw-w-full tw-items-center tw-justify-between">
              <div className="tw-flex tw-items-center tw-space-x-4">
                <div className="tw-flex tw-bg-white tw-rounded-lg tw-shadow-sm tw-border tw-border-gray-200 tw-p-2">
                  <Radio.Group
                    value={previewMode}
                    onChange={(e) => setPreviewMode(e.target.value)}
                    buttonStyle="solid"
                    size="small"
                    className="tw-flex"
                  >
                    <Radio.Button
                      value="desktop"
                      className="tw-flex tw-items-center tw-justify-center"
                    >
                      <Monitor className="tw-w-4 tw-h-4" />
                    </Radio.Button>
                    <Radio.Button
                      value="tablet"
                      className="tw-flex tw-items-center tw-justify-center"
                    >
                      <Tablet className="tw-w-4 tw-h-4" />
                    </Radio.Button>
                    <Radio.Button
                      value="mobile"
                      className="tw-flex tw-items-center tw-justify-center"
                    >
                      <Smartphone className="tw-w-4 tw-h-4" />
                    </Radio.Button>
                  </Radio.Group>
                </div>

                <button
                  onClick={() => setShowSettings(true)}
                  className="tw-flex tw-items-center tw-px-3 tw-py-2 tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-rounded-lg tw-transition-colors"
                >
                  <Settings className="tw-w-4 tw-h-4 tw-mr-2" />
                  Page Settings
                </button>
              </div>

              <div className="tw-flex tw-items-center tw-space-x-3">
                <button
                  onClick={onCancel}
                  className="tw-px-4 tw-py-2 tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-rounded-lg tw-transition-colors"
                >
                  Cancel
                </button>

                <button
                  onClick={handleSave}
                  disabled={saving || !pageData.name || !pageData.slug}
                  className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-4 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all tw-flex tw-items-center tw-disabled:tw-opacity-50"
                >
                  {saving ? (
                    <>
                      <div className="tw-animate-spin tw-rounded-full tw-h-4 tw-w-4 tw-border-b-2 tw-border-white tw-mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="tw-w-4 tw-h-4 tw-mr-2" />
                      Save Page
                    </>
                  )}
                </button>
              </div>
            </div>
            {/* Toggle Page Structure inside the same control group */}
            {!isStructureOpen && (
              <div className=" tw-flex tw-items-center tw-justify-center tw-border-l tw-border-gray-200">
                <Tooltip
                  title={
                    isStructureOpen
                      ? "Hide Page Structure"
                      : "Show Page Structure"
                  }
                >
                  <button
                    onClick={() => setIsStructureOpen((v) => !v)}
                    className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                  >
                    <ChevronLeft
                      size={30}
                      className={` ${isStructureOpen ? "" : "tw-rotate-180 "}`}
                    />
                  </button>
                </Tooltip>
              </div>
            )}
          </div>

          {/* Canvas */}
          <div className="tw-flex-1 tw-bg-gray-100 tw-p-6 tw-overflow-auto">
            <div className="tw-flex tw-justify-center">
              <div
                className={`tw-bg-white tw-shadow-lg tw-rounded-lg tw-overflow-hidden ${getPreviewModeStyles()}`}
              >
                <div className="tw-relative tw-w-full tw-h-full">
                  <iframe
                    srcDoc={generatePreviewHTML()}
                    className="tw-w-full tw-h-full tw-border-0"
                    title="Page Preview"
                  />
                  <PreviewDropZone />
                </div>
              </div>
            </div>
          </div>
        </div>

        <PageStructure
          isStructureOpen={isStructureOpen}
          setIsStructureOpen={setIsStructureOpen}
          pageData={pageData}
          setPageData={setPageData}
          components={components}
          handleCssChange={handleCssChange}
          removeComponentFromPage={removeComponentFromPage}
        />
      </div>

      {/* Page Settings Modal */}
      {showSettings && (
        <PageSetting
          showSettings={showSettings}
          setShowSettings={setShowSettings}
          pageData={pageData}
          setPageData={setPageData}
        />
      )}
    </DndProvider>
  );
};

export default DragDropBuilder;
