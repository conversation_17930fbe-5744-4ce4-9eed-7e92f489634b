import React, { useState, useEffect, useRef } from "react";
import { Radio, Tooltip, Input } from "antd";
import Header from "../Layout/Header";
import {
  Save,
  X,
  Eye,
  Smartphone,
  Tablet,
  Monitor,
  Settings,
  Trash2,
  GripVertical,
  ChevronLeft,
} from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";

const DragDropBuilder = ({ page, onSave, onCancel }) => {
  const api = useHttp();
  const [components, setComponents] = useState([]);
  const [categories, setCategories] = useState([]);
  const [pageData, setPageData] = useState({
    name: "",
    slug: "",
    meta_title: "",
    meta_description: "",
    custom_css: "",
    custom_js: "",
    components: [],
  });
  const [previewMode, setPreviewMode] = useState("desktop");
  const [showSettings, setShowSettings] = useState(false);
  const [saving, setSaving] = useState(false);
  const [isStructureOpen, setIsStructureOpen] = useState(true);
  const [isDraggingStructure, setIsDraggingStructure] = useState(false);
  const [tempComponents, setTempComponents] = useState([]);

  useEffect(() => {
    // Fetch components
    api.sendRequest(CONSTANTS.API.components.get, (res) => {
      console.log("Components fetched:", res);
      setComponents(res);
    });

    // Fetch categories
    api.sendRequest(CONSTANTS.API.categories.get, (res) => {
      console.log("Categories fetched:", res);
      setCategories(res);
    });

    if (page) {
      setPageData({
        name: page.name || "",
        slug: page.slug || "",
        meta_title: page.meta_title || "",
        meta_description: page.meta_description || "",
        custom_css: page.custom_css || "",
        custom_js: page.custom_js || "",
        components: page.components || [],
      });
    }
  }, [page]);

  const handleSave = async () => {
    setSaving(true);

    const apiConfig = page
      ? apiGenerator(CONSTANTS.API.pages.update, { id: page.id })
      : CONSTANTS.API.pages.create;

    api.sendRequest(
      apiConfig,
      (res) => {
        console.log("Page saved successfully:", res);
        setSaving(false);
        onSave();
      },
      pageData,
      page ? "Page updated successfully!" : "Page created successfully!",
      (error) => {
        console.error("Error saving page:", error);
        setSaving(false);
      }
    );
  };

  const addComponentToPage = (component) => {
    const newPageComponent = {
      id: component.id,
      name: component.name,
      order: pageData.components.length,
      cssClass: "",
    };

    setPageData({
      ...pageData,
      components: [...pageData.components, newPageComponent],
    });
  };

  const removeComponentFromPage = (index) => {
    const updatedComponents = pageData.components.filter((_, i) => i !== index);
    setPageData({
      ...pageData,
      components: updatedComponents,
    });
  };

  // Reorder helper (reserved for react-dnd integration)
  const moveComponent = (fromIndex, toIndex) => {
    const updatedComponents = [...pageData.components];
    const [movedComponent] = updatedComponents.splice(fromIndex, 1);
    updatedComponents.splice(toIndex, 0, movedComponent);

    setPageData({
      ...pageData,
      components: updatedComponents,
    });
  };

  // Simple move component with debouncing
  const moveComponentWithDebounce = (fromIndex, toIndex) => {
    if (isDraggingStructure) {
      // During drag: update temp components only
      const currentComponents =
        tempComponents.length > 0 ? tempComponents : pageData.components;
      const updatedComponents = [...currentComponents];
      const [movedComponent] = updatedComponents.splice(fromIndex, 1);
      updatedComponents.splice(toIndex, 0, movedComponent);
      setTempComponents(updatedComponents);
    } else {
      // Not dragging: update pageData directly
      moveComponent(fromIndex, toIndex);
    }
  };

  // Finish drag and apply changes
  const finishDrag = () => {
    if (tempComponents.length > 0) {
      setPageData({
        ...pageData,
        components: tempComponents,
      });
      setTempComponents([]);
    }
    setIsDraggingStructure(false);
  };

  // Update CSS class for a specific component in page structure
  const handleCssChange = (index, value) => {
    const updated = [...pageData.components];
    updated[index] = { ...updated[index], cssClass: value };
    setPageData({ ...pageData, components: updated });
  };

  const getPreviewModeStyles = () => {
    switch (previewMode) {
      case "mobile":
        return "tw-w-80 tw-h-96";
      case "tablet":
        return "tw-w-96 tw-h-96";
      default:
        return "tw-w-full tw-h-96";
    }
  };

  const generatePreviewHTML = () => {
    let html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${pageData.meta_title || pageData.name}</title>
        <meta name="description" content="${pageData.meta_description}">
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
          ${pageData.custom_css}
        </style>
      </head>
      <body>
    `;

    pageData.components.forEach((pageComp) => {
      const component = components.find((c) => c.id === pageComp.id);
      if (component) {
        let componentHTML = component.html_content;

        // Replace placeholders with sample data
        if (component.placeholders) {
          component.placeholders.forEach((placeholder) => {
            const sampleData = getSampleData(placeholder);
            componentHTML = componentHTML.replace(
              new RegExp(`\\$\\{${placeholder}\\}`, "g"),
              sampleData
            );
          });
        }

        // Wrap with optional user CSS class
        const cssClass = pageComp.cssClass
          ? ` class="${pageComp.cssClass}"`
          : "";
        html += `<div${cssClass}>${componentHTML}</div>\n`;
      }
    });

    // Add empty state if no components
    if (pageData.components.length === 0) {
      html += `
        <div style="display: flex; align-items: center; justify-content: center; min-height: 100vh; text-align: center; color: #6b7280;">
          <div>
            <div style="font-size: 3rem; margin-bottom: 1rem;">📄</div>
            <p style="font-size: 1.125rem; margin-bottom: 0.5rem;">Drop components here to build your page</p>
            <p style="font-size: 0.875rem;">Drag components from the left panel</p>
          </div>
        </div>
      `;
    }

    html += `
        <script>
          ${pageData.custom_js}
        </script>
      </body>
      </html>
    `;

    return html;
  };

  const getSampleData = (placeholder) => {
    const lowerPlaceholder = placeholder.toLowerCase();

    if (
      lowerPlaceholder.includes("title") ||
      lowerPlaceholder.includes("heading")
    ) {
      return "Sample Page Title";
    } else if (lowerPlaceholder.includes("subtitle")) {
      return "This is a sample subtitle";
    } else if (
      lowerPlaceholder.includes("content") ||
      lowerPlaceholder.includes("text")
    ) {
      return "This is sample content that demonstrates how your page will look with real data.";
    } else if (
      lowerPlaceholder.includes("image") ||
      lowerPlaceholder.includes("img")
    ) {
      return "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=400&fit=crop";
    } else if (
      lowerPlaceholder.includes("button") ||
      lowerPlaceholder.includes("cta")
    ) {
      return "Learn More";
    } else {
      return `[${placeholder}]`;
    }
  };

  const groupedComponents = categories.reduce((acc, category) => {
    acc[category.id] = {
      name: category.name,
      color: category.color,
      components: components.filter((comp) => comp.category_id === category.id),
    };
    return acc;
  }, {});

  // React DnD types
  const DND_TYPES = {
    LIB_ITEM: "LIB_ITEM",
    STRUCT_ITEM: "STRUCT_ITEM",
  };

  // Draggable Component Library item
  const LibraryItem = ({ comp }) => {
    const [{ isDragging }, drag] = useDrag(
      () => ({
        type: DND_TYPES.LIB_ITEM,
        item: { component: comp },
        collect: (monitor) => ({ isDragging: monitor.isDragging() }),
      }),
      [comp]
    );

    return (
      <div
        ref={drag}
        className="tw-p-3 tw-bg-gray-50 tw-rounded-lg tw-border tw-border-gray-200 tw-cursor-move tw-hover:tw-bg-gray-100 tw-hover:tw-border-gray-300 tw-transition-colors"
        style={{ opacity: isDragging ? 0.6 : 1 }}
      >
        <div className="tw-flex tw-items-center tw-justify-between">
          <div className="tw-flex-1">
            <p className="tw-text-sm tw-font-medium tw-text-gray-900">
              {comp.name}
            </p>
            <p className="tw-text-xs tw-text-gray-500">
              {comp.placeholders ? comp.placeholders.length : 0} placeholders
            </p>
          </div>
          <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" />
        </div>
      </div>
    );
  };

  // Preview Drop Zone Component - Fixed flickering issue
  const PreviewDropZone = () => {
    const [{ isOver, canDrop }, drop] = useDrop(
      () => ({
        accept: DND_TYPES.LIB_ITEM,
        drop: (item) => {
          addComponentToPage(item.component);
        },
        collect: (monitor) => ({
          isOver: monitor.isOver({ shallow: true }),
          canDrop: monitor.canDrop(),
        }),
      }),
      [addComponentToPage]
    );

    const showDropIndicator = isOver && canDrop;

    return (
      <div
        ref={drop}
        className={`tw-absolute tw-inset-0 tw-z-10 tw-transition-all tw-duration-150 ${
          showDropIndicator
            ? "tw-bg-blue-50/60 tw-border-2 tw-border-dashed tw-border-blue-400"
            : ""
        }`}
        style={{
          minHeight: "400px",
          pointerEvents: canDrop ? "auto" : "none",
        }}
      >
        {showDropIndicator && (
          <div className="tw-flex tw-items-center tw-justify-center tw-h-full tw-pointer-events-none">
            <div className="tw-bg-white tw-rounded-lg tw-p-6 tw-shadow-xl tw-border-2 tw-border-blue-300 tw-animate-pulse">
              <div className="tw-flex tw-items-center tw-space-x-3">
                <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce"></div>
                <p className="tw-text-blue-700 tw-font-semibold tw-text-lg">
                  Drop to add component
                </p>
                <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce tw-animation-delay-100"></div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Reorderable structure item - SIMPLIFIED
  const StructureItem = ({ index, componentName, onRemove }) => {
    const ref = useRef(null);

    const [, drop] = useDrop({
      accept: DND_TYPES.STRUCT_ITEM,
      hover(item, monitor) {
        if (!ref.current) return;
        const dragIndex = item.index;
        const hoverIndex = index;
        if (dragIndex === hoverIndex) return;

        const hoverRect = ref.current.getBoundingClientRect();
        const hoverMiddleY = (hoverRect.bottom - hoverRect.top) / 2;
        const clientOffset = monitor.getClientOffset();
        const hoverClientY = clientOffset.y - hoverRect.top;

        if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;
        if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;

        // Use the debounced move function
        moveComponentWithDebounce(dragIndex, hoverIndex);
        item.index = hoverIndex;
      },
    });

    const [{ isDragging }, drag] = useDrag(
      () => ({
        type: DND_TYPES.STRUCT_ITEM,
        item: () => {
          // Start dragging
          setIsDraggingStructure(true);
          setTempComponents([...pageData.components]);
          return { index };
        },
        collect: (monitor) => ({ isDragging: monitor.isDragging() }),
      }),
      [index]
    );

    // Handle drag end with useEffect
    const prevIsDragging = useRef(isDragging);
    useEffect(() => {
      if (prevIsDragging.current && !isDragging) {
        // Drag just ended
        finishDrag();
      }
      prevIsDragging.current = isDragging;
    }, [isDragging]);

    drag(drop(ref));

    return (
      <div
        ref={ref}
        className="tw-flex tw-items-center tw-justify-between tw-p-3 tw-bg-gray-50 tw-rounded-lg tw-border tw-border-gray-200"
        style={{ opacity: isDragging ? 0.6 : 1 }}
      >
        <div className="tw-flex tw-items-center">
          <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400 tw-mr-2" />
          <div>
            <p className="tw-text-sm tw-font-medium tw-text-gray-900">
              {componentName || "Unknown Component"}
            </p>
            <p className="tw-text-xs tw-text-gray-500">Position {index + 1}</p>
            <div className="tw-mt-2">
              <Input
                size="small"
                placeholder="Enter class"
                value={pageData.components[index]?.cssClass || ""}
                onChange={(e) => handleCssChange(index, e.target.value)}
              />
            </div>
          </div>
        </div>
        <button
          onClick={() => onRemove(index)}
          className="tw-p-1 tw-text-gray-400 tw-hover:tw-text-red-600 tw-rounded"
        >
          <Trash2 className="tw-w-4 tw-h-4" />
        </button>
      </div>
    );
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="tw-h-screen tw-flex tw-overflow-hidden">
        {/* Left Sidebar - Components Library */}
        <div className="tw-w-80 tw-bg-white tw-border-r tw-border-gray-200 tw-flex tw-flex-col">
          <div className="tw-p-4 tw-border-b tw-border-gray-200">
            <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-2">
              Component Library
            </h3>
            <p className="tw-text-xs tw-text-gray-600">
              Drag components to the canvas to build your page
            </p>
          </div>

          <div className="tw-flex-1 tw-overflow-y-auto tw-p-4">
            {Object.entries(groupedComponents).map(
              ([categoryId, categoryData]) => (
                <div key={categoryId} className="tw-mb-6">
                  <div className="tw-flex tw-items-center tw-mb-3">
                    <div
                      className="tw-w-3 tw-h-3 tw-rounded-full tw-mr-2"
                      style={{ backgroundColor: categoryData.color }}
                    />
                    <h4 className="tw-font-medium tw-text-gray-900">
                      {categoryData.name}
                    </h4>
                  </div>

                  <div className="tw-space-y-2">
                    {categoryData.components.map((component) => (
                      <LibraryItem key={component.id} comp={component} />
                    ))}
                  </div>
                </div>
              )
            )}

            {Object.keys(groupedComponents).length === 0 && (
              <div className="tw-text-center tw-py-8">
                <p className="tw-text-gray-500">No components available</p>
                <p className="tw-text-sm tw-text-gray-400 tw-mt-1">
                  Create components first to use them here
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Main Canvas Area */}
        <div className="tw-flex-1 tw-flex tw-flex-col">
          {/* Top Toolbar */}
          <div className="tw-flex tw-w-full tw-bg-white tw-border-b tw-border-gray-200 tw-space-x-4">
            <div className="tw-p-[21px]  tw-flex tw-w-full tw-items-center tw-justify-between">
              <div className="tw-flex tw-items-center tw-space-x-4">
                <div className="tw-flex tw-bg-white tw-rounded-lg tw-shadow-sm tw-border tw-border-gray-200 tw-p-2">
                  <Radio.Group
                    value={previewMode}
                    onChange={(e) => setPreviewMode(e.target.value)}
                    buttonStyle="solid"
                    size="small"
                    className="tw-flex"
                  >
                    <Radio.Button
                      value="desktop"
                      className="tw-flex tw-items-center tw-justify-center"
                    >
                      <Monitor className="tw-w-4 tw-h-4" />
                    </Radio.Button>
                    <Radio.Button
                      value="tablet"
                      className="tw-flex tw-items-center tw-justify-center"
                    >
                      <Tablet className="tw-w-4 tw-h-4" />
                    </Radio.Button>
                    <Radio.Button
                      value="mobile"
                      className="tw-flex tw-items-center tw-justify-center"
                    >
                      <Smartphone className="tw-w-4 tw-h-4" />
                    </Radio.Button>
                  </Radio.Group>
                </div>

                <button
                  onClick={() => setShowSettings(true)}
                  className="tw-flex tw-items-center tw-px-3 tw-py-2 tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-rounded-lg tw-transition-colors"
                >
                  <Settings className="tw-w-4 tw-h-4 tw-mr-2" />
                  Page Settings
                </button>
              </div>

              <div className="tw-flex tw-items-center tw-space-x-3">
                <button
                  onClick={onCancel}
                  className="tw-px-4 tw-py-2 tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-rounded-lg tw-transition-colors"
                >
                  Cancel
                </button>

                <button
                  onClick={handleSave}
                  disabled={saving || !pageData.name || !pageData.slug}
                  className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-4 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all tw-flex tw-items-center tw-disabled:tw-opacity-50"
                >
                  {saving ? (
                    <>
                      <div className="tw-animate-spin tw-rounded-full tw-h-4 tw-w-4 tw-border-b-2 tw-border-white tw-mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="tw-w-4 tw-h-4 tw-mr-2" />
                      Save Page
                    </>
                  )}
                </button>
              </div>
            </div>
            {/* Toggle Page Structure inside the same control group */}
            {!isStructureOpen && (
              <div className=" tw-flex tw-items-center tw-justify-center tw-border-l tw-border-gray-200">
                <Tooltip
                  title={
                    isStructureOpen
                      ? "Hide Page Structure"
                      : "Show Page Structure"
                  }
                >
                  <button
                    onClick={() => setIsStructureOpen((v) => !v)}
                    className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                  >
                    <ChevronLeft
                      size={30}
                      className={` ${isStructureOpen ? "" : "tw-rotate-180 "}`}
                    />
                  </button>
                </Tooltip>
              </div>
            )}
          </div>

          {/* Canvas */}
          <div className="tw-flex-1 tw-bg-gray-100 tw-p-6 tw-overflow-auto">
            <div className="tw-flex tw-justify-center">
              <div
                className={`tw-bg-white tw-shadow-lg tw-rounded-lg tw-overflow-hidden ${getPreviewModeStyles()}`}
              >
                <div className="tw-relative tw-w-full tw-h-full">
                  <iframe
                    srcDoc={generatePreviewHTML()}
                    className="tw-w-full tw-h-full tw-border-0"
                    title="Page Preview"
                  />
                  <PreviewDropZone />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Sidebar - Page Structure (collapsible, animated) */}
        {/* In-layout column with width animation */}
        <div
          className={`tw-bg-white tw-border-l tw-border-gray-200 tw-flex tw-flex-col tw-transition-all tw-duration-300 tw-ease-in-out ${
            isStructureOpen ? "tw-w-80" : "tw-w-0 tw-overflow-hidden"
          }`}
        >
          {isStructureOpen && (
            <div className=" tw-border-b tw-border-gray-200 tw-flex tw-items-center tw-justify-between">
              <div className="tw-p-4 tw-flex tw-items-start tw-flex-col tw-justify-start tw-text-start">
                <h3 className=" tw-text-lg tw-font-semibold tw-text-gray-900  ">
                  Page Structure
                </h3>
                <p className="tw-text-sm tw-text-gray-600 tw-mt-1">
                  {
                    (isDraggingStructure && tempComponents.length > 0
                      ? tempComponents
                      : pageData.components
                    ).length
                  }{" "}
                  components
                </p>
              </div>
              <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-border-l tw-border-gray-200">
                <Tooltip
                  title={
                    isStructureOpen
                      ? "Hide Page Structure"
                      : "Show Page Structure"
                  }
                >
                  <button
                    onClick={() => setIsStructureOpen((v) => !v)}
                    className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                  >
                    <ChevronLeft
                      size={30}
                      className={` ${isStructureOpen ? "" : "tw-rotate-180 "}`}
                    />
                  </button>
                </Tooltip>
              </div>
            </div>
          )}

          <div className="tw-flex-1 tw-overflow-y-auto tw-p-4">
            {(() => {
              // Use temp components during drag, actual components otherwise
              const currentComponents =
                isDraggingStructure && tempComponents.length > 0
                  ? tempComponents
                  : pageData.components;

              return currentComponents.length > 0 ? (
                <div className="tw-space-y-2">
                  {currentComponents.map((pageComp, index) => {
                    const component = components.find(
                      (c) => c.id === pageComp.id
                    );
                    return (
                      <StructureItem
                        key={`${pageComp.id}-${index}`}
                        index={index}
                        componentName={component?.name}
                        onRemove={removeComponentFromPage}
                      />
                    );
                  })}
                </div>
              ) : (
                <div className="tw-text-center tw-py-8">
                  <p className="tw-text-gray-500">No components added</p>
                  <p className="tw-text-sm tw-text-gray-400 tw-mt-1">
                    Components will appear here as you add them
                  </p>
                </div>
              );
            })()}
          </div>
        </div>
      </div>

      {/* Page Settings Modal */}
      {showSettings && (
        <div className="tw-fixed tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-flex tw-items-center tw-justify-center tw-z-50">
          <div className="tw-bg-white tw-rounded-xl tw-p-6 tw-w-full tw-max-w-2xl tw-m-4 tw-max-h-screen tw-overflow-y-auto">
            <div className="tw-flex tw-justify-between tw-items-center tw-mb-6">
              <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900">
                Page Settings
              </h3>
              <button
                onClick={() => setShowSettings(false)}
                className="tw-text-gray-400 tw-hover:tw-text-gray-600"
              >
                <X className="tw-w-5 tw-h-5" />
              </button>
            </div>

            <div className="tw-space-y-4">
              <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-gap-4">
                <div>
                  <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                    Page Name *
                  </label>
                  <input
                    type="text"
                    value={pageData.name}
                    onChange={(e) =>
                      setPageData({ ...pageData, name: e.target.value })
                    }
                    className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                    placeholder="e.g., Home, About, Contact"
                    required
                  />
                </div>

                <div>
                  <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                    URL Slug *
                  </label>
                  <input
                    type="text"
                    value={pageData.slug}
                    onChange={(e) =>
                      setPageData({ ...pageData, slug: e.target.value })
                    }
                    className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                    placeholder="e.g., home, about, contact"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                  Meta Title
                </label>
                <input
                  type="text"
                  value={pageData.meta_title}
                  onChange={(e) =>
                    setPageData({ ...pageData, meta_title: e.target.value })
                  }
                  className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                  placeholder="SEO title for this page"
                />
              </div>

              <div>
                <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                  Meta Description
                </label>
                <textarea
                  value={pageData.meta_description}
                  onChange={(e) =>
                    setPageData({
                      ...pageData,
                      meta_description: e.target.value,
                    })
                  }
                  className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-h-20"
                  placeholder="Brief description for search engines"
                  rows="3"
                />
              </div>

              <div>
                <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                  Custom CSS
                </label>
                <textarea
                  value={pageData.custom_css}
                  onChange={(e) =>
                    setPageData({ ...pageData, custom_css: e.target.value })
                  }
                  className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-h-32 tw-font-mono tw-text-sm"
                  placeholder="/* Custom CSS for this page */"
                  rows="6"
                />
              </div>

              <div>
                <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                  Custom JavaScript
                </label>
                <textarea
                  value={pageData.custom_js}
                  onChange={(e) =>
                    setPageData({ ...pageData, custom_js: e.target.value })
                  }
                  className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-h-32 tw-font-mono tw-text-sm"
                  placeholder="// Custom JavaScript for this page"
                  rows="6"
                />
              </div>
            </div>

            <div className="tw-flex tw-justify-end tw-space-x-3 tw-mt-6">
              <button
                onClick={() => setShowSettings(false)}
                className="tw-px-4 tw-py-2 tw-border tw-border-gray-300 tw-text-gray-700 tw-rounded-lg tw-font-medium tw-hover:tw-bg-gray-50 tw-transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </DndProvider>
  );
};

export default DragDropBuilder;
