@tailwind base;
@tailwind components;
@tailwind utilities;

.component-tab-list:where(
    .css-dev-only-do-not-override-1vjf2v5
  ).ant-radio-button-wrapper {
  /* position: relative;
  display: inline-block;
  height: 32px;
  margin: 0;
  padding-inline: 15px;
  padding-block: 0;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  line-height: 30px;
  background: #ffffff; */
  border: 0px;
  border-block-start-width: 0px;
  border-inline-start-width: 0;
  border-inline-end-width: 0px;
  /* cursor: pointer;
  transition: color 0.2s, background 0.2s, box-shadow 0.2s; */
}

.component-tab-list {
  :where(
      .css-dev-only-do-not-override-1vjf2v5
    ).ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    background: #2563eb !important;
    border-color: #2563eb !important;
    color: #fff;
  }

  :where(
      .css-dev-only-do-not-override-1vjf2v5
    ).ant-radio-button-wrapper-checked:not(
      .ant-radio-button-wrapper-disabled
    ):first-child {
    border-color: #2563eb;
  }
}

/* Sidebar Collapse Animation */
.sidebar-collapse-transition {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mobile Sidebar Drawer Styles */
.tw-mobile-sidebar .ant-drawer-body {
  padding: 0 !important;
}

.tw-mobile-sidebar .ant-drawer-header {
  border-bottom: 1px solid #f0f0f0 !important;
  padding: 16px 24px !important;
}

/* Sidebar Tooltip Styles */
.ant-tooltip {
  z-index: 1060 !important;
}

/* Smooth transitions for sidebar items */
.sidebar-nav-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-nav-item:hover {
  transform: translateX(2px);
}

/* Collapse button hover effect */
.sidebar-collapse-btn {
  transition: all 0.2s ease-in-out;
}

.sidebar-collapse-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

/* Mobile responsive adjustments */
@media (max-width: 1023px) {
  .sidebar-desktop {
    display: none !important;
  }
}

@media (min-width: 1024px) {
  .sidebar-mobile {
    display: none !important;
  }
}

/* Enhanced Search Input Styles */
.search-input-enhanced .ant-input-affix-wrapper {
  border-radius: 8px !important;
  border: 1px solid #d1d5db !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.search-input-enhanced .ant-input-affix-wrapper:hover {
  border-color: #9ca3af !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
}

.search-input-enhanced .ant-input-affix-wrapper-focused {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.search-input-enhanced .ant-input {
  font-size: 14px !important;
  padding: 8px 12px !important;
}

.search-input-enhanced .ant-input-prefix {
  margin-right: 8px !important;
}

/* Search loading animation */
.search-loading {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.custom-color-picker:where(
    .css-dev-only-do-not-override-3pokpj
  ).ant-color-picker-trigger {
  width: 100% !important;
}

.custom-color-picker:where(
    .css-dev-only-do-not-override-3pokpj
  ).ant-color-picker-trigger
  .ant-color-picker-color-block {
  width: 100% !important;
}

/* Sidebar Menu Styles */
.sidebar-desktop .ant-menu {
  background: transparent !important;
  border: none !important;
}

.sidebar-desktop .ant-menu-item {
  margin: 4px 0 !important;
  border-radius: 8px !important;
  height: auto !important;
  line-height: 1.5 !important;
  padding: 12px 16px !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  color: #6b7280 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
}

.sidebar-desktop .ant-menu-item:hover {
  background: #f3f4f6 !important;
  color: #111827 !important;
  transform: translateX(2px);
}

.sidebar-desktop .ant-menu-item-selected {
  background: linear-gradient(to right, #2563eb, #7c3aed) !important;
  color: white !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.sidebar-desktop .ant-menu-item-selected:hover {
  background: linear-gradient(to right, #1d4ed8, #6d28d9) !important;
  color: white !important;
  transform: translateX(2px);
}

.sidebar-desktop .ant-menu-item-selected::after {
  display: none !important;
}

/* Collapsed sidebar menu styles */
.sidebar-desktop .ant-menu-inline-collapsed .ant-menu-item {
  padding: 12px !important;
  text-align: center !important;
}

.sidebar-desktop
  .ant-menu-inline-collapsed
  .ant-menu-item
  .ant-menu-title-content {
  display: none !important;
}

/* Mobile sidebar menu styles */
.tw-mobile-sidebar .ant-menu {
  background: transparent !important;
  border: none !important;
}

.tw-mobile-sidebar .ant-menu-item {
  margin: 4px 0 !important;
  border-radius: 8px !important;
  height: auto !important;
  line-height: 1.5 !important;
  padding: 12px 16px !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  color: #6b7280 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
}

.tw-mobile-sidebar .ant-menu-item:hover {
  background: #f3f4f6 !important;
  color: #111827 !important;
}

.tw-mobile-sidebar .ant-menu-item-selected {
  background: linear-gradient(to right, #2563eb, #7c3aed) !important;
  color: white !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.tw-mobile-sidebar .ant-menu-item-selected:hover {
  background: linear-gradient(to right, #1d4ed8, #6d28d9) !important;
  color: white !important;
}

.tw-mobile-sidebar .ant-menu-item-selected::after {
  display: none !important;
}

/* Menu icon styles */
.ant-menu-item .anticon {
  margin-right: 12px !important;
}

.ant-menu-inline-collapsed .ant-menu-item .anticon {
  margin-right: 0 !important;
}
