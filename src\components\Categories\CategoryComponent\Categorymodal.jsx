import {
  But<PERSON>,
  ColorPicker,
  Divider,
  Form,
  Input,
  Modal,
  Space,
  Tooltip,
  Typography,
} from "antd";
import { Palette } from "lucide-react";

const { Text } = Typography;
const { TextArea } = Input;

const Categorymodal = ({
  showForm,
  setShowForm,
  editingCategory,
  setEditingCategory,
  saving,
  setSaving,
  formData,
  setFormData,
  handleSubmit,
  predefinedColors,
  form,
  resetForm,
}) => {
  return (
    <>
      <Modal
        title={
          <Space>
            <Palette className="tw-w-5 tw-h-5 tw-text-blue-600" />
            {editingCategory ? "Edit Category" : "Add New Category"}
          </Space>
        }
        open={showForm}
        onCancel={resetForm}
        footer={null}
        width={600}
        className="tw-top-8"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="tw-mt-6"
          size="large"
          requiredMark={false}
        >
          <Form.Item
            name="name"
            label={
              <span className="tw-text-sm tw-font-medium tw-text-gray-700">
                Category Name
              </span>
            }
            rules={[
              {
                required: true,
                message: "Please enter a category name",
              },
              {
                min: 2,
                message: "Category name must be at least 2 characters",
              },
            ]}
          >
            <Input
              placeholder="e.g., Headers, Content, Forms"
              className="tw-rounded-lg"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label={
              <span className="tw-text-sm tw-font-medium tw-text-gray-700">
                Description
              </span>
            }
          >
            <TextArea
              placeholder="Brief description of this category"
              rows={4}
              className="tw-rounded-lg"
            />
          </Form.Item>

          <div className="tw-mb-6">
            <Text className="tw-text-sm tw-font-medium tw-text-gray-700 tw-block tw-mb-3">
              Category Color
            </Text>
            <div className="tw-flex tw-flex-wrap tw-gap-2 tw-mb-3">
              {predefinedColors.map((color) => (
                <Tooltip key={color} title={`Select ${color}`}>
                  <button
                    type="button"
                    onClick={() => {
                      setFormData({ ...formData, color });
                      // Also update the form field value
                      form.setFieldValue("color", color);
                    }}
                    className={`tw-w-8 tw-h-8 tw-rounded-full tw-border-2 tw-transition-all tw-cursor-pointer hover:tw-scale-110 ${
                      formData.color === color
                        ? "tw-border-gray-900 tw-scale-110 tw-shadow-lg"
                        : "tw-border-gray-300 hover:tw-border-gray-400"
                    }`}
                    style={{ backgroundColor: color }}
                  />
                </Tooltip>
              ))}
            </div>
            <Form.Item name="color">
              <ColorPicker
                className="custom-color-picker"
                onChange={(color) => {
                  const hexColor = color.toHexString();
                  setFormData({
                    ...formData,
                    color: hexColor,
                  });
                  // Also update the form field value
                  form.setFieldValue("color", hexColor);
                }}
                size="large"
                format="hex"
              />
            </Form.Item>
          </div>

          <Divider className="tw-my-6" />

          <div className="tw-flex tw-justify-end tw-gap-3">
            <Button
              size="large"
              onClick={resetForm}
              className="tw-px-6 tw-h-12 tw-rounded-lg"
            >
              Cancel
            </Button>
            <Button
              type="primary"
              size="large"
              htmlType="submit"
              loading={saving}
              // icon={saving ? null : <SaveOutlined />}
              className="tw-px-6 tw-h-12 tw-rounded-lg tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-border-0 hover:tw-from-blue-700 hover:tw-to-purple-700"
            >
              {saving
                ? "Saving..."
                : editingCategory
                ? "Update Category"
                : "Create Category"}
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  );
};

export default Categorymodal;
