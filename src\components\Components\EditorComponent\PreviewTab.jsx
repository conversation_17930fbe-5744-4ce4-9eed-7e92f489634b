import { Tooltip } from "antd";
import { Eye } from "lucide-react";
import React, { useState } from "react";
import { deviceConfigs, getPreviewHTML } from "../content";

const PreviewTab = ({ formData }) => {
  const [deviceType, setDeviceType] = useState("laptop"); // laptop, tablet, mobile
  const deviceConfig = deviceConfigs(deviceType);
  return (
    <div className="tw-p-0 tw-mb-4">
      <div className="tw-flex tw-flex-col tw-items-center tw-mb-6">
        <div className="tw-flex tw-items-center tw-space-x-2  tw-rounded-lg  tw-mb-1">
          {Object.entries(deviceConfig)?.map(([key, config]) => (
            <Tooltip
              key={key}
              title={`${config.label} (${config.description})`}
            >
              <button
                type="button"
                onClick={() => setDeviceType(key)}
                className={`tw-flex tw-items-center tw-justify-center tw-w-8 tw-h-8 tw-rounded-md tw-transition-all tw-duration-200 ${
                  deviceType === key
                    ? "tw-bg-[#EAF0FD] tw-text-white tw-shadow-sm"
                    : "tw-text-gray-500 tw-hover:tw-bg-gray-100 tw-hover:tw-text-gray-700"
                }`}
              >
                {config.icon}
              </button>
            </Tooltip>
          ))}
        </div>
      </div>

      {/* Preview Container */}
      {formData?.html_content ? (
        <div
          className={`tw-flex tw-justify-center tw-items-start tw-min-h-[500px] tw-bg-gray-50 tw-rounded-lg tw-border tw-border-gray-200 tw-transition-all tw-duration-300 ${
            deviceType === "mobile"
              ? "tw-p-2"
              : deviceType === "tablet"
              ? "tw-p-4"
              : "tw-p-6"
          }`}
        >
          <div
            className="tw-bg-white tw-rounded-lg tw-shadow-xl tw-overflow-hidden tw-transition-all tw-duration-300 tw-border tw-border-gray-200"
            style={{
              width: deviceConfig?.[deviceType]?.width,
              maxWidth: deviceType === "laptop" ? "100%" : "95%",
              height: deviceConfig?.[deviceType]?.height,
              maxHeight:
                deviceType === "laptop"
                  ? "600px"
                  : deviceConfig[deviceType].height,
            }}
          >
            {/* Device Frame Header (for mobile/tablet) */}
            {deviceType !== "laptop" && (
              <div className="tw-h-6 tw-bg-gray-100 tw-flex tw-items-center tw-justify-center tw-border-b tw-border-gray-200">
                <div className="tw-flex tw-space-x-1">
                  <div className="tw-w-2 tw-h-2 tw-bg-gray-400 tw-rounded-full"></div>
                  <div className="tw-w-2 tw-h-2 tw-bg-gray-400 tw-rounded-full"></div>
                  <div className="tw-w-2 tw-h-2 tw-bg-gray-400 tw-rounded-full"></div>
                </div>
              </div>
            )}

            <iframe
              srcDoc={getPreviewHTML(formData)}
              className="tw-w-full tw-border-0"
              title="Component Preview"
              style={{
                height: deviceType !== "laptop" ? "calc(100% - 24px)" : "100%",
              }}
            />
          </div>
        </div>
      ) : (
        <div
          className={`tw-flex tw-justify-center tw-items-start tw-min-h-[500px] tw-bg-gray-50 tw-rounded-lg tw-border tw-border-gray-200 tw-transition-all tw-duration-300 ${
            deviceType === "mobile"
              ? "tw-p-2"
              : deviceType === "tablet"
              ? "tw-p-4"
              : "tw-p-6"
          }`}
        >
          <div
            className="tw-bg-white tw-rounded-lg tw-shadow-xl tw-overflow-hidden tw-transition-all tw-duration-300 tw-border-2 tw-border-dashed tw-border-gray-300"
            style={{
              width: deviceConfig?.[deviceType]?.width,
              maxWidth: deviceType === "laptop" ? "100%" : "95%",
              height: deviceConfig?.[deviceType]?.height,
              maxHeight:
                deviceType === "laptop"
                  ? "600px"
                  : deviceConfig[deviceType].height,
            }}
          >
            {/* Device Frame Header (for mobile/tablet) */}
            {deviceType !== "laptop" && (
              <div className="tw-h-6 tw-bg-gray-100 tw-flex tw-items-center tw-justify-center tw-border-b tw-border-gray-200">
                <div className="tw-flex tw-space-x-1">
                  <div className="tw-w-2 tw-h-2 tw-bg-gray-400 tw-rounded-full"></div>
                  <div className="tw-w-2 tw-h-2 tw-bg-gray-400 tw-rounded-full"></div>
                  <div className="tw-w-2 tw-h-2 tw-bg-gray-400 tw-rounded-full"></div>
                </div>
              </div>
            )}

            {/* Empty State Content */}
            <div className="tw-h-full tw-flex tw-items-center tw-justify-center tw-p-6">
              <div className="tw-text-center">
                <Eye className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                <p className="tw-text-gray-500 tw-mb-2">
                  Add HTML content to see preview
                </p>
                <p className="tw-text-sm tw-text-gray-400">
                  Preview will be available in mobile, tablet, and desktop views
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PreviewTab;
